import asyncio
from threading import Thread
import websockets
from func.tools.singleton_mode import singleton

@singleton
class CommonWebsocket:
    def __init__(self):
        self.clients = set()
        self._initializing = False

    async def handler(self, websocket, path):
        self.clients.add(websocket)
        try:
            async for message in websocket:
                print(f"Received: {message}")
                await self.broadcast(message)
        finally:
            self.clients.remove(websocket)

    async def send_data(self, message):
        if self.clients:
            await self.broadcast(message)

    async def broadcast(self, message):
        if self.clients:
            await asyncio.gather(
                *[client.send(message) for client in self.clients]
            )

    def run_forever_thread(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        start_server = websockets.serve(self.handler, "0.0.0.0", 18765)
        loop.run_until_complete(start_server)
        loop.run_forever()

    def start(self):
        Thread(target=self.run_forever_thread).start() 